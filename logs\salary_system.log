2025-06-26 11:06:29.230 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 11:06:29.230 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 11:06:29.230 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 11:06:29.230 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 11:06:29.230 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 11:06:29.230 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 11:07:46.581 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 11:07:46.581 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 11:07:46.582 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 11:07:46.582 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 11:07:46.583 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 11:07:46.584 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 11:07:48.052 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-26 11:07:48.053 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-26 11:07:48.054 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:07:48.054 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:07:48.055 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 11:07:48.056 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 11:07:48.057 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 11:07:48.076 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 11:07:48.080 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 11:07:48.085 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:07:48.090 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 11:07:48.092 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-26 11:07:48.122 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-26 11:07:48.135 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-26 11:07:48.136 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-26 11:07:48.931 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1493 | 菜单栏创建完成
2025-06-26 11:07:48.932 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 11:07:48.932 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 11:07:48.933 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 11:07:48.947 | INFO     | src.gui.prototype.prototype_main_window:__init__:1469 | 菜单栏管理器初始化完成
2025-06-26 11:07:48.948 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-26 11:07:48.950 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2170 | 管理器设置完成，包含增强版表头管理器
2025-06-26 11:07:48.997 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-26 11:07:49.000 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-26 11:07:49.108 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-26 11:07:49.109 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-26 11:07:49.127 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:916 | 开始从元数据动态加载工资数据...
2025-06-26 11:07:49.129 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:923 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-26 11:07:49.163 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:654 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-26 11:07:49.182 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:689 | 恢复导航状态: 90个展开项
2025-06-26 11:07:49.211 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年 > 4月', '异动人员表 > 2025年']
2025-06-26 11:07:49.215 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:495 | 增强导航面板初始化完成
2025-06-26 11:07:49.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-26 11:07:49.314 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-26 11:07:49.315 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:07:49.316 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 | 表头编辑管理器初始化完成
2025-06-26 11:07:49.320 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1851 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-26 11:07:49.321 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 11:07:49.322 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 11:07:49.332 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 10.87ms
2025-06-26 11:07:49.333 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 11:07:49.378 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-26 11:07:49.467 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-26 11:07:49.488 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:07:49.489 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 11:07:49.490 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2143 | 快捷键设置完成
2025-06-26 11:07:49.491 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2132 | 主窗口UI设置完成。
2025-06-26 11:07:49.491 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2198 | 信号连接设置完成
2025-06-26 11:07:49.493 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:07:49.554 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2627 | 已加载字段映射信息，共44个表的映射
2025-06-26 11:07:49.554 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 11:07:49.554 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 11:07:49.556 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 11:07:49.556 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.04ms
2025-06-26 11:07:49.557 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 11:07:49.558 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:07:49.558 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 11:07:49.559 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 11:07:49.560 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 11:07:49.560 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.06ms
2025-06-26 11:07:49.561 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 11:07:49.562 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:07:49.562 | INFO     | src.gui.prototype.prototype_main_window:__init__:2084 | 原型主窗口初始化完成
2025-06-26 11:07:50.391 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-26 11:07:50.396 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-26 11:07:50.397 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1241 | MainWorkspaceArea 响应式适配: sm
2025-06-26 11:07:50.400 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:938 | 执行延迟的工资数据加载...
2025-06-26 11:07:50.401 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 11:07:50.401 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: 📊 工资表
2025-06-26 11:07:50.444 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 68 个匹配类型 'salary_data' 的表
2025-06-26 11:07:50.447 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026年，包含 5 个月份
2025-06-26 11:07:50.476 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025年，包含 3 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制刷新: 11 个年份, 17 个月份
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 11:07:50.477 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_salary_data 执行完成
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年 > 4月', '异动人员表 > 2025年']
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年
2025-06-26 11:08:03.727 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 11:08:03.727 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:08:03.727 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 11:08:03.727 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:08:03.727 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年', '工资表', '异动人员表', '异动人员表 > 2025年 > 4月']
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月
2025-06-26 11:08:05.826 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 11:08:05.826 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:08:05.826 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 11:08:05.826 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:08:05.826 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月
2025-06-26 11:08:07.040 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 11:08:07.040 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > A岗职工
2025-06-26 11:08:07.041 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:08:07.042 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 11:08:07.042 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_01_a_grade_employees，第1页，每页50条
2025-06-26 11:08:07.043 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_01_a_grade_employees 第1页
2025-06-26 11:08:07.044 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > A岗职工
2025-06-26 11:08:07.044 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_01_a_grade_employees 第1页数据，每页50条
2025-06-26 11:08:07.046 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:08:07.054 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 11:08:07.054 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:08:07.056 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_a_grade_employees 没有保存的字段映射信息
2025-06-26 11:08:07.056 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 11:08:07.057 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 11:08:07.062 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:08:07.063 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:08:07.064 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2696 | 应用表 salary_data_2026_01_a_grade_employees 的字段偏好: 7个字段 ['id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary']
2025-06-26 11:08:07.066 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 11:08:07.068 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 11:08:07.075 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 1000 -> 50
2025-06-26 11:08:07.076 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 11:08:07.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 7 列
2025-06-26 11:08:07.114 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 39.73ms
2025-06-26 11:08:07.144 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 11:08:07.144 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 11:08:09.699 | ERROR    | src.gui.prototype.prototype_main_window:_refresh_table_data:1129 | 刷新表格数据失败: 'MainWorkspaceArea' object has no attribute 'current_data_path'
2025-06-26 11:08:12.404 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 11:08:12.404 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 11:08:22.900 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 11:08:22.900 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:08:22.915 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:08:22.915 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:08:22.915 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 11:08:23.172 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 12
2025-06-26 11:08:23.172 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-26 11:08:23.172 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-26 11:08:23.244 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 329.11ms
2025-06-26 11:08:23.257 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-26 11:08:23.258 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 11:08:34.892 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 11:08:34.893 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:08:34.893 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:08:34.894 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:08:34.896 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 11:08:34.898 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 12 -> 50
2025-06-26 11:08:34.898 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 11:08:34.900 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 11:08:34.909 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 10.78ms
2025-06-26 11:08:34.909 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第1页, 显示50条记录，字段数: 16
2025-06-26 11:08:34.909 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 1
2025-06-26 11:08:50.270 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 11:08:50.270 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > 离休人员
2025-06-26 11:08:50.270 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:08:50.270 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(2条)，使用普通模式
2025-06-26 11:08:50.270 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > 离休人员
2025-06-26 11:08:50.270 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 11:08:50.286 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_01_retired_employees 获取数据...
2025-06-26 11:08:50.286 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_01_retired_employees 获取 2 行数据。
2025-06-26 11:08:50.286 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 11:08:50.286 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_retired_employees 没有保存的字段映射信息
2025-06-26 11:08:50.286 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 11:08:50.286 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:08:50.286 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_01_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 11:08:50.302 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 11:08:50.302 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 11:08:50.673 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 2
2025-06-26 11:08:50.673 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 11:08:50.704 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 11:08:50.704 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 402.56ms
2025-06-26 11:08:50.719 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 11:08:57.034 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 11:08:57.034 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > 退休人员
2025-06-26 11:08:57.034 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:08:57.034 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(13条)，使用普通模式
2025-06-26 11:08:57.034 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > 退休人员
2025-06-26 11:08:57.034 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 11:08:57.034 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_01_pension_employees 获取数据...
2025-06-26 11:08:57.034 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_01_pension_employees 获取 13 行数据。
2025-06-26 11:08:57.034 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 11:08:57.034 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_pension_employees 没有保存的字段映射信息
2025-06-26 11:08:57.049 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 11:08:57.049 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:08:57.049 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_01_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 11:08:57.049 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 11:08:57.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 13
2025-06-26 11:08:57.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 11:08:57.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 11:08:57.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 0.00ms
2025-06-26 11:08:57.049 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 11:09:03.380 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 11:09:03.380 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 11:09:03.380 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:09:03.380 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(1396条)，启用分页模式
2025-06-26 11:09:03.380 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_01_active_employees，第1页，每页50条
2025-06-26 11:09:03.395 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_01_active_employees 第1页
2025-06-26 11:09:03.395 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 11:09:03.395 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_01_active_employees 第1页数据，每页50条
2025-06-26 11:09:03.395 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:09:03.395 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 11:09:03.395 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:09:03.395 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_active_employees 没有保存的字段映射信息
2025-06-26 11:09:03.395 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 11:09:03.395 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 11:09:03.411 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:09:03.411 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:09:03.411 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_01_active_employees 无字段偏好设置，显示所有字段
2025-06-26 11:09:03.411 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 11:09:03.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 50
2025-06-26 11:09:03.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 11:09:03.411 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 11:09:03.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 11:09:03.442 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 30.99ms
2025-06-26 11:09:03.451 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 11:09:03.454 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 11:09:13.277 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 11:09:13.277 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:09:13.277 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:09:13.277 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:09:13.277 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 11:09:13.277 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 11:09:13.277 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 11:09:13.277 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 11:09:18.907 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 5630.29ms
2025-06-26 11:09:18.907 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-26 11:09:18.923 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 11:09:25.538 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 11:09:25.539 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:09:25.540 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:09:25.540 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:09:25.543 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 11:09:25.545 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 11:09:25.546 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 11:09:25.547 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 11:09:31.359 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 5814.13ms
2025-06-26 11:09:31.359 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第1页, 显示50条记录，字段数: 16
2025-06-26 11:09:31.359 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 1
2025-06-26 11:09:39.980 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 11:09:39.980 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 4月
2025-06-26 11:09:39.980 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 11:09:39.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 11:09:39.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 11:09:40.011 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 15.68ms
2025-06-26 11:09:40.011 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 11:09:40.011 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:09:40.011 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 11:09:40.011 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 11:09:40.011 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 11:09:40.011 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 0.00ms
2025-06-26 11:09:40.011 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 11:09:40.011 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:09:40.011 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 4月
2025-06-26 11:09:42.287 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 4月', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表']
2025-06-26 11:09:42.287 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 4月 > A岗职工
2025-06-26 11:09:42.287 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:09:42.287 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 11:09:42.287 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_04_a_grade_employees，第1页，每页50条
2025-06-26 11:09:42.287 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_04_a_grade_employees 第1页
2025-06-26 11:09:42.287 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 4月 > A岗职工
2025-06-26 11:09:42.287 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_04_a_grade_employees 第1页数据，每页50条
2025-06-26 11:09:42.287 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:09:42.302 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 11:09:42.302 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:09:42.302 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_04_a_grade_employees: 7 个字段重命名
2025-06-26 11:09:42.302 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 11:09:42.302 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 11:09:42.318 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:09:42.318 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:09:42.318 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2696 | 应用表 salary_data_2026_04_a_grade_employees 的字段偏好: 2个字段 ['id_card', 'basic_salary']
2025-06-26 11:09:42.318 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 11:09:42.318 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 11:09:42.318 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 11:09:42.318 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 11:09:42.318 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 2 个表头
2025-06-26 11:09:42.335 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 2 列
2025-06-26 11:09:42.348 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 30.17ms
2025-06-26 11:09:42.350 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 11:09:42.353 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 11:09:48.551 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 11:09:48.551 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 11:09:56.487 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 11:09:56.487 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:09:56.487 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:09:56.487 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:09:56.487 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 11:09:56.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 12
2025-06-26 11:09:56.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-26 11:09:56.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 11:09:56.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-26 11:09:56.518 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 30.87ms
2025-06-26 11:09:56.518 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-26 11:09:56.518 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 11:10:14.162 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 11:10:14.162 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:10:14.163 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:10:14.163 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:10:14.166 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 11:10:14.169 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 12 -> 50
2025-06-26 11:10:14.169 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 11:10:14.169 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 11:10:14.170 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 11:10:14.177 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 7.99ms
2025-06-26 11:10:14.177 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第1页, 显示50条记录，字段数: 16
2025-06-26 11:10:14.177 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 1
2025-06-26 11:10:40.033 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 4月', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表']
2025-06-26 11:10:40.033 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 4月 > 全部在职人员
2025-06-26 11:10:40.033 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 11:10:40.033 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(1396条)，启用分页模式
2025-06-26 11:10:40.033 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_04_active_employees，第1页，每页50条
2025-06-26 11:10:40.033 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_04_active_employees 第1页
2025-06-26 11:10:40.033 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 4月 > 全部在职人员
2025-06-26 11:10:40.033 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_04_active_employees 第1页数据，每页50条
2025-06-26 11:10:40.033 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:10:40.050 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 11:10:40.050 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:10:40.050 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_04_active_employees: 7 个字段重命名
2025-06-26 11:10:40.050 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 11:10:40.050 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 11:10:40.064 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:10:40.064 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:10:40.064 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_04_active_employees 无字段偏好设置，显示所有字段
2025-06-26 11:10:40.064 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 11:10:40.064 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 11:10:40.064 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 11:10:40.064 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 11:10:40.064 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 11:10:40.079 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 11:10:40.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 39.45ms
2025-06-26 11:10:40.115 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 11:10:40.116 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 11:10:48.308 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 11:10:48.308 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:10:48.323 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 11:10:48.323 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:10:48.323 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 11:10:48.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 11:10:48.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 11:10:48.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 11:10:48.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 11:10:53.372 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 5048.22ms
2025-06-26 11:10:53.372 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-26 11:10:53.372 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 11:11:04.259 | INFO     | __main__:main:302 | 应用程序正常退出
2025-06-26 11:37:18.398 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 11:37:18.399 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 11:37:18.399 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 11:37:18.399 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 11:37:18.400 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 11:37:18.400 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 11:37:19.226 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:37:19.226 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:38:36.672 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 11:38:36.673 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 11:38:36.673 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 11:38:36.673 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 11:38:36.674 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 11:38:36.674 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 11:38:37.482 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:40:04.157 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 11:40:04.158 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 11:40:04.158 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 11:40:04.158 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 11:40:04.159 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 11:40:04.159 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 11:40:04.958 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:40:04.959 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:40:04.960 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 11:40:04.960 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 11:40:04.961 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 11:40:04.970 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 11:40:04.971 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 11:40:04.972 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:40:04.973 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 11:40:04.989 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:40:04.994 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 11:40:04.995 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:40:04.998 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 11:40:05.005 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:40:05.010 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 11:40:05.010 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:40:05.010 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 11:41:44.806 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 11:41:44.806 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 11:41:44.807 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 11:41:44.807 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 11:41:44.807 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 11:41:44.808 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 11:41:45.609 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:41:45.610 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:41:45.610 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 11:41:45.611 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 11:41:45.612 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 11:41:45.623 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 11:41:45.623 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 11:41:45.624 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:41:45.625 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 11:41:45.628 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:41:45.634 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 11:41:45.634 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:41:45.638 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 11:41:45.650 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 11:41:45.650 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 11:41:45.650 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 11:41:45.650 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 11:45:21.785 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 11:45:21.785 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 11:45:21.786 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 11:45:21.786 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 11:45:21.786 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 11:45:21.788 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 11:45:23.017 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:45:23.018 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 11:45:23.018 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 11:45:23.019 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 11:45:23.019 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 11:45:23.040 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 11:45:23.042 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 11:45:23.043 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:45:23.043 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 11:45:23.044 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_04_a_grade_employees 分页获取数据: 第1页, 每页1条
2025-06-26 11:45:23.048 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_04_a_grade_employees 获取第1页数据: 1 行，总计62行
2025-06-26 11:45:23.048 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 11:45:23.380 | INFO     | src.gui.table_field_preference_dialog:load_current_preference:279 | 加载表 salary_data_2026_04_a_grade_employees 的偏好设置: 7 个字段
2025-06-26 11:45:23.381 | INFO     | src.gui.table_field_preference_dialog:__init__:74 | 表级字段偏好对话框初始化完成: salary_data_2026_04_a_grade_employees
