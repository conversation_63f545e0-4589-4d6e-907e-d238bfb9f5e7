{"timestamp": "2025-06-24T10:27:43.927800", "table_name": "salary_data_2025_05_test", "action": "save", "field_count": 7, "fields": ["工号", "姓名", "部门名称", "2025年岗位工资", "2025年薪级工资", "津贴", "应发工资"]}
{"timestamp": "2025-06-24T10:59:03.547003", "table_name": "工资数据表", "action": "save", "field_count": 6, "fields": ["工号", "姓名", "基本工资", "岗位工资", "绩效工资", "应发合计"]}
{"timestamp": "2025-06-24T10:59:03.547578", "table_name": "异动人员表", "action": "save", "field_count": 5, "fields": ["员工编号", "员工姓名", "异动类型", "异动原因", "生效日期"]}
{"timestamp": "2025-06-24T10:59:32.033073", "table_name": "工资数据表", "action": "save", "field_count": 6, "fields": ["工号", "姓名", "基本工资", "岗位工资", "绩效工资", "应发合计"]}
{"timestamp": "2025-06-24T11:00:10.041974", "table_name": "test_table", "action": "save", "field_count": 3, "fields": ["test_field_1", "test_field_2", "test_field_3"]}
{"timestamp": "2025-06-24T14:05:16.950588", "table_name": "test_field_mapping_table", "action": "save", "field_count": 9, "fields": ["工号", "姓名", "部门名称", "2025年岗位工资", "2025年薪级工资", "津贴", "2025年奖励性绩效预发", "应发工资", "2025公积金"]}
{"timestamp": "2025-06-24T14:05:16.957372", "table_name": "test_field_mapping_table", "action": "save", "field_count": 9, "fields": ["工号", "姓名", "部门名称", "2025年岗位工资", "2025年薪级工资", "津贴", "2025年奖励性绩效预发", "应发工资", "2025公积金"]}
{"timestamp": "2025-06-24T14:05:41.430386", "table_name": "test_table", "action": "save", "field_count": 2, "fields": ["test_field", "test_field2"]}
{"timestamp": "2025-06-24T14:05:41.430386", "table_name": "persistence_test_table", "action": "save", "field_count": 3, "fields": ["原始字段1", "原始字段2", "原始字段3"]}
{"timestamp": "2025-06-24T14:05:52.582658", "table_name": "test_table", "action": "save", "field_count": 2, "fields": ["test_field", "test_field2"]}
{"timestamp": "2025-06-24T14:05:57.007108", "table_name": "persistence_test_table", "action": "save", "field_count": 3, "fields": ["原始字段1", "原始字段2", "原始字段3"]}
{"timestamp": "2025-06-24T14:05:58.579075", "table_name": "test_table", "action": "save", "field_count": 2, "fields": ["test_field", "test_field2"]}
{"timestamp": "2025-06-24T14:05:58.594785", "table_name": "persistence_test_table", "action": "save", "field_count": 3, "fields": ["原始字段1", "原始字段2", "原始字段3"]}
{"timestamp": "2025-06-24T14:14:10.259823", "table_name": "test_field_mapping_table", "action": "save", "field_count": 9, "fields": ["工号", "姓名", "部门名称", "2025年岗位工资", "2025年薪级工资", "津贴", "2025年奖励性绩效预发", "应发工资", "2025公积金"]}
{"timestamp": "2025-06-24T14:14:10.275531", "table_name": "test_field_mapping_table", "action": "save", "field_count": 9, "fields": ["工号", "姓名", "部门名称", "2025年岗位工资", "2025年薪级工资", "津贴", "2025年奖励性绩效预发", "应发工资", "2025公积金"]}
{"timestamp": "2025-06-24T14:14:28.809927", "table_name": "test_table", "action": "save", "field_count": 2, "fields": ["test_field", "test_field2"]}
{"timestamp": "2025-06-24T14:14:28.814052", "table_name": "test_table", "action": "field_update", "field_name": "test_field", "old_value": "测试字段", "new_value": "新测试字段"}
{"timestamp": "2025-06-24T14:14:28.818865", "table_name": "persistence_test_table", "action": "save", "field_count": 3, "fields": ["原始字段1", "原始字段2", "原始字段3"]}
{"timestamp": "2025-06-24T15:09:11.488238", "table_name": "test_new_table", "action": "field_update", "field_name": "employee_id", "old_value": "employee_id", "new_value": "工号"}
{"timestamp": "2025-06-24T15:09:11.492728", "table_name": "test_new_table", "action": "field_update", "field_name": "employee_id", "old_value": "工号", "new_value": "员工编号"}
{"timestamp": "2025-06-24T15:12:06.968912", "table_name": "salary_data_2025_01_pension_employees", "action": "field_update", "field_name": "employee_id", "old_value": "employee_id", "new_value": "工号"}
{"timestamp": "2025-06-24T15:15:20.939489", "table_name": "salary_data_2022_01_a_grade_employees", "action": "field_update", "field_name": "employee_name", "old_value": "employee_name", "new_value": "姓名"}
{"timestamp": "2025-06-24T15:20:28.045675", "table_name": "salary_data_2021_12_retired_employees", "action": "field_update", "field_name": "employee_name", "old_value": "employee_name", "new_value": "姓名"}
{"timestamp": "2025-06-24T19:02:53.927943", "table_name": "salary_data_2025_01_pension_employees", "action": "field_update", "field_name": "employee_id", "old_value": "工号", "new_value": "工号"}
{"timestamp": "2025-06-24T23:19:04.075576", "table_name": "salary_data_2025_01_test", "action": "save", "field_count": 5, "fields": ["工号", "姓名", "部门名称", "基本工资", "绩效工资"]}
{"timestamp": "2025-06-24T23:19:04.075576", "table_name": "salary_data_2025_01_test", "action": "field_update", "field_name": "工号", "old_value": "员工工号", "new_value": "员工编号"}
{"timestamp": "2025-06-24T23:39:13.925997", "table_name": "salary_data_2021_01_retired_employees", "action": "save", "field_count": 16, "fields": ["id", "employee_id", "employee_name", "id_card", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary", "month", "year", "created_at", "updated_at"]}
{"timestamp": "2025-06-24T23:39:13.957596", "table_name": "salary_data_2021_01_pension_employees", "action": "save", "field_count": 16, "fields": ["id", "employee_id", "employee_name", "id_card", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary", "month", "year", "created_at", "updated_at"]}
{"timestamp": "2025-06-24T23:39:13.973439", "table_name": "salary_data_2021_01_active_employees", "action": "save", "field_count": 16, "fields": ["id", "employee_id", "employee_name", "id_card", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary", "month", "year", "created_at", "updated_at"]}
{"timestamp": "2025-06-24T23:39:14.017464", "table_name": "salary_data_2021_01_a_grade_employees", "action": "save", "field_count": 16, "fields": ["id", "employee_id", "employee_name", "id_card", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary", "month", "year", "created_at", "updated_at"]}
{"timestamp": "2025-06-24T23:39:14.035829", "table_name": "salary_data_2021_01_retired_employees", "action": "save", "field_count": 16, "fields": ["employee_id", "employee_name", "id_card", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary", "month", "year", "created_at", "updated_at", "id"]}
{"timestamp": "2025-06-24T23:39:14.043753", "table_name": "salary_data_2021_01_pension_employees", "action": "save", "field_count": 16, "fields": ["employee_id", "employee_name", "id_card", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary", "month", "year", "created_at", "updated_at", "id"]}
{"timestamp": "2025-06-24T23:39:14.043753", "table_name": "salary_data_2021_01_active_employees", "action": "save", "field_count": 16, "fields": ["employee_id", "employee_name", "id_card", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary", "month", "year", "created_at", "updated_at", "id"]}
{"timestamp": "2025-06-24T23:39:14.059777", "table_name": "salary_data_2021_01_a_grade_employees", "action": "save", "field_count": 16, "fields": ["employee_id", "employee_name", "id_card", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary", "month", "year", "created_at", "updated_at", "id"]}
{"timestamp": "2025-06-24T23:42:49.796542", "table_name": "salary_data_2021_01_retired_employees", "action": "save", "field_count": 16, "fields": ["id", "employee_id", "employee_name", "id_card", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary", "month", "year", "created_at", "updated_at"]}
{"timestamp": "2025-06-24T23:42:49.796542", "table_name": "salary_data_2021_01_pension_employees", "action": "save", "field_count": 16, "fields": ["id", "employee_id", "employee_name", "id_card", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary", "month", "year", "created_at", "updated_at"]}
{"timestamp": "2025-06-24T23:42:49.812252", "table_name": "salary_data_2021_01_active_employees", "action": "save", "field_count": 16, "fields": ["id", "employee_id", "employee_name", "id_card", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary", "month", "year", "created_at", "updated_at"]}
{"timestamp": "2025-06-24T23:42:49.849125", "table_name": "salary_data_2021_01_a_grade_employees", "action": "save", "field_count": 16, "fields": ["id", "employee_id", "employee_name", "id_card", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary", "month", "year", "created_at", "updated_at"]}
{"timestamp": "2025-06-25T00:09:46.636003", "table_name": "salary_data_2020_01_retired_employees", "action": "field_update", "field_name": "employee_id", "old_value": "employee_id", "new_value": "工号"}
{"timestamp": "2025-06-25T00:09:54.330938", "table_name": "salary_data_2020_01_retired_employees", "action": "field_update", "field_name": "employee_name", "old_value": "employee_name", "new_value": "姓名"}
{"timestamp": "2025-06-25T00:10:04.919630", "table_name": "salary_data_2020_01_pension_employees", "action": "field_update", "field_name": "employee_id", "old_value": "employee_id", "new_value": "工号"}
{"timestamp": "2025-06-25T00:10:11.030277", "table_name": "salary_data_2020_01_pension_employees", "action": "field_update", "field_name": "employee_name", "old_value": "employee_name", "new_value": "姓名"}
{"timestamp": "2025-06-25T00:10:28.241246", "table_name": "salary_data_2020_01_a_grade_employees", "action": "field_update", "field_name": "employee_id", "old_value": "employee_id", "new_value": "工号"}
{"timestamp": "2025-06-25T00:10:34.918044", "table_name": "salary_data_2020_01_a_grade_employees", "action": "field_update", "field_name": "employee_name", "old_value": "employee_name", "new_value": "姓名"}
{"timestamp": "2025-06-25T08:30:36.189757", "table_name": "salary_data_2019_12_a_grade_employees", "action": "field_update", "field_name": "employee_name", "old_value": "employee_name", "new_value": "姓名"}
{"timestamp": "2025-06-25T10:11:24.279592", "table_name": "salary_data_retired_employees_2025_06_离休人员工资表", "action": "save", "field_count": 12, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "合计"]}
{"timestamp": "2025-06-25T10:11:24.456916", "table_name": "salary_data_pension_employees_2025_06_退休人员工资表", "action": "save", "field_count": 21, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "应发工资"]}
{"timestamp": "2025-06-25T10:11:24.668153", "table_name": "salary_data_active_employees_2025_全部在职人员工资表", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-06-25T10:11:24.862152", "table_name": "salary_data_a_grade_employees_2025_A岗职工", "action": "save", "field_count": 17, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "2025年奖励性绩效预发", "借支", "应发工资", "2025公积金"]}
{"timestamp": "2025-06-25T10:28:10.351626", "table_name": "salary_data_2018_06_a_grade_employees", "action": "field_update", "field_name": "employee_id", "old_value": "employee_id", "new_value": "工号"}
{"timestamp": "2025-06-25T10:28:17.201869", "table_name": "salary_data_2018_06_a_grade_employees", "action": "field_update", "field_name": "employee_name", "old_value": "employee_name", "new_value": "姓名"}
{"timestamp": "2025-06-25T10:29:41.570133", "table_name": "salary_data_2017_01_retired_employees", "action": "save", "field_count": 12, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "合计"]}
{"timestamp": "2025-06-25T10:29:41.829478", "table_name": "salary_data_2017_01_pension_employees", "action": "save", "field_count": 21, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "应发工资"]}
{"timestamp": "2025-06-25T10:29:42.136176", "table_name": "salary_data_2017_01_active_employees", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-06-25T10:29:42.397346", "table_name": "salary_data_2017_01_a_grade_employees", "action": "save", "field_count": 17, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "2025年奖励性绩效预发", "借支", "应发工资", "2025公积金"]}
{"timestamp": "2025-06-25T11:05:58.129100", "table_name": "salary_data_2022_12_retired_employees", "action": "field_update", "field_name": "employee_name", "old_value": "employee_name", "new_value": "姓名"}
{"timestamp": "2025-06-25T11:08:27.651820", "table_name": "salary_data_2016_01_retired_employees", "action": "save", "field_count": 12, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "合计"]}
{"timestamp": "2025-06-25T11:08:27.850112", "table_name": "salary_data_2016_01_pension_employees", "action": "save", "field_count": 21, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "应发工资"]}
{"timestamp": "2025-06-25T11:08:28.056780", "table_name": "salary_data_2016_01_active_employees", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-06-25T11:08:28.268807", "table_name": "salary_data_2016_01_a_grade_employees", "action": "save", "field_count": 17, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "2025年奖励性绩效预发", "借支", "应发工资", "2025公积金"]}
{"timestamp": "2025-06-25T19:44:17.550106", "table_name": "salary_data_2026_02_retired_employees", "action": "save", "field_count": 12, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "合计"]}
{"timestamp": "2025-06-25T19:44:17.864593", "table_name": "salary_data_2026_02_pension_employees", "action": "save", "field_count": 21, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "应发工资"]}
{"timestamp": "2025-06-25T19:44:18.361844", "table_name": "salary_data_2026_02_active_employees", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-06-25T19:44:18.719392", "table_name": "salary_data_2026_02_a_grade_employees", "action": "save", "field_count": 17, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "2025年奖励性绩效预发", "借支", "应发工资", "2025公积金"]}
{"timestamp": "2025-06-25T21:28:19.667269", "table_name": "test_table", "action": "save", "field_count": 4, "fields": ["工号", "姓名", "部门", "basic_salary"]}
{"timestamp": "2025-06-25T21:28:19.673456", "table_name": "test_table", "action": "field_update", "field_name": "new_field", "old_value": "new_field", "new_value": "新字段"}
{"timestamp": "2025-06-25T21:28:19.680326", "table_name": "test_preference_table", "action": "save_header_preference", "field_count": 2, "fields": ["selected_fields", "field_count"]}
{"timestamp": "2025-06-25T21:28:20.005117", "table_name": "salary_data_2025_05_active_employees", "action": "save", "field_count": 5, "fields": ["工号", "姓名", "部门名称", "2025年岗位工资", "应发工资"]}
{"timestamp": "2025-06-25T21:46:16.694678", "table_name": "salary_data_2026_11_retired_employees", "action": "save", "field_count": 12, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "合计"]}
{"timestamp": "2025-06-25T21:46:16.971454", "table_name": "salary_data_2026_11_pension_employees", "action": "save", "field_count": 21, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "应发工资"]}
{"timestamp": "2025-06-25T21:46:17.299260", "table_name": "salary_data_2026_11_active_employees", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-06-25T21:46:17.592320", "table_name": "salary_data_2026_11_a_grade_employees", "action": "save", "field_count": 17, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "2025年奖励性绩效预发", "借支", "应发工资", "2025公积金"]}
{"timestamp": "2025-06-25T21:47:33.008378", "table_name": "salary_data_2026_11_a_grade_employees", "action": "field_update", "field_name": "employee_id", "old_value": "employee_id", "new_value": "工号"}
{"timestamp": "2025-06-25T21:47:39.743743", "table_name": "salary_data_2026_11_a_grade_employees", "action": "field_update", "field_name": "employee_name", "old_value": "employee_name", "new_value": "姓名"}
{"timestamp": "2025-06-25T21:58:58.194293", "table_name": "salary_data_2026_11", "action": "save", "field_count": 5, "fields": ["工号", "姓名", "部门名称", "2026年岗位工资", "应发工资"]}
{"timestamp": "2025-06-25T21:58:58.201466", "table_name": "salary_data_2026_11", "action": "save_header_preference", "field_count": 2, "fields": ["selected_fields", "field_count"]}
{"timestamp": "2025-06-25T22:00:57.813718", "table_name": "test_table", "action": "save", "field_count": 2, "fields": ["工号", "姓名"]}
{"timestamp": "2025-06-25T22:00:57.816461", "table_name": "test_table", "action": "save_header_preference", "field_count": 2, "fields": ["selected_fields", "field_count"]}
{"timestamp": "2025-06-25T22:35:24.391478", "table_name": "test_table_2026_11", "action": "save", "field_count": 3, "fields": ["工号", "姓名", "部门名称"]}
{"timestamp": "2025-06-25T22:35:24.588154", "table_name": "test_table_2026_11", "action": "save_header_preference", "field_count": 2, "fields": ["selected_fields", "field_count"]}
{"timestamp": "2025-06-25T22:41:58.653614", "table_name": "salary_data_2026_01_retired_employees", "action": "save_header_preference", "field_count": 2, "fields": ["selected_fields", "field_count"]}
{"timestamp": "2025-06-25T22:46:00.558666", "table_name": "salary_data_2026_03_retired_employees", "action": "save", "field_count": 12, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "合计"]}
{"timestamp": "2025-06-25T22:46:00.901443", "table_name": "salary_data_2026_03_pension_employees", "action": "save", "field_count": 21, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "应发工资"]}
{"timestamp": "2025-06-25T22:46:01.234317", "table_name": "salary_data_2026_03_active_employees", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-06-25T22:46:01.562455", "table_name": "salary_data_2026_03_a_grade_employees", "action": "save", "field_count": 17, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "2025年奖励性绩效预发", "借支", "应发工资", "2025公积金"]}
{"timestamp": "2025-06-25T23:23:32.437796", "table_name": "salary_data_2026_03_active_employees", "action": "save_header_preference", "field_count": 2, "fields": ["selected_fields", "field_count"]}
{"timestamp": "2025-06-25T23:25:08.940227", "table_name": "salary_data_2026_03_a_grade_employees", "action": "save_header_preference", "field_count": 2, "fields": ["selected_fields", "field_count"]}
{"timestamp": "2025-06-26T00:04:14.284492", "table_name": "salary_data_2026_04_retired_employees", "action": "save", "field_count": 8, "fields": ["id", "employee_id", "employee_name", "department", "基本离休费", "allowance", "护理费", "合计"]}
{"timestamp": "2025-06-26T00:04:14.614373", "table_name": "salary_data_2026_04_pension_employees", "action": "save", "field_count": 17, "fields": ["id", "employee_id", "employee_name", "department", "position", "基本退休费", "allowance", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "total_salary"]}
{"timestamp": "2025-06-26T00:04:14.910363", "table_name": "salary_data_2026_04_active_employees", "action": "save", "field_count": 16, "fields": ["id", "employee_id", "employee_name", "department", "position", "position_salary", "grade_salary", "allowance", "performance", "卫生费", "车补", "补发", "借支", "total_salary", "social_insurance", "代扣代存养老保险"]}
{"timestamp": "2025-06-26T00:04:15.241356", "table_name": "salary_data_2026_04_a_grade_employees", "action": "save", "field_count": 13, "fields": ["id", "employee_id", "employee_name", "department", "position", "position_salary", "2025年校龄salary", "allowance", "performance", "卫生费", "借支", "total_salary", "social_insurance"]}
{"timestamp": "2025-06-26T00:07:19.664611", "table_name": "salary_data_2026_04_a_grade_employees", "action": "save_header_preference", "field_count": 2, "fields": ["selected_fields", "field_count"]}
{"timestamp": "2025-06-26T09:17:00.681704", "table_name": "salary_data_2026_04_retired_employees", "action": "field_update", "field_name": "total_salary", "old_value": "total_salary", "new_value": "合计"}
{"timestamp": "2025-06-26T09:20:28.942848", "table_name": "salary_data_2026_04_active_employees", "action": "save_header_preference", "field_count": 2, "fields": ["selected_fields", "field_count"]}
{"timestamp": "2025-06-26T09:53:09.842465", "table_name": "salary_data_2026_04_a_grade_employees", "action": "save_header_preference", "field_count": 2, "fields": ["selected_fields", "field_count"]}
{"timestamp": "2025-06-26T11:02:33.072007", "table_name": "salary_data_2026_01_a_grade_employees", "action": "save_table_field_preference", "field_count": 2, "fields": ["preferred_fields", "field_count"]}
{"timestamp": "2025-06-26T11:02:33.152529", "table_name": "salary_data_2026_04_a_grade_employees", "action": "save_table_field_preference", "field_count": 2, "fields": ["preferred_fields", "field_count"]}
